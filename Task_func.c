#include "Task_func.h"
#include "motor_app.h"
#include "sine_wave.h"

uint8_t control_mode=0;
uint8_t open=0;


void contorol_Task(void)
{
	if(open)
	{
			if(control_mode==0)
			{
				app_pid_calc();
			}

	}

}
uint8_t state_data=2;

void state_up(void)
{
	switch(state_data)
	{
		case 0:
			
			break;
		case 1:
		//	pid_target(xy_z[0][0],xy_z[0][1]);
			open=1;
			break;
		case 2:
//			pid_target(xy[0][0],xy[0][1]);
			open=1;
			break;
	}
}