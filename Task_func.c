#include "Task_func.h"
#include "motor_app.h"
#include "sine_wave.h"

uint8_t control_mode=0;
uint8_t open=0;


// 高性能控制任务 - 减少条件判断
void contorol_Task(void)
{
	// 使用位运算优化条件判断
	if(open & (control_mode == 0))  // 只有当open=1且control_mode=0时才执行
	{
		app_pid_calc();
	}
}
uint8_t state_data=2;

void state_up(void)
{
	switch(state_data)
	{
		case 0:
			
			break;
		case 1:
		//	pid_target(xy_z[0][0],xy_z[0][1]);
			open=1;
			break;
		case 2:
//			pid_target(xy[0][0],xy[0][1]);
			open=1;
			break;
	}
}