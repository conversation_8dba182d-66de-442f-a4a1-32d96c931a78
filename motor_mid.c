#include "motor_mid.h"

unsigned char RE[6]={0x01,0xF3,0xAB,0x01,0x00,0x6B};
unsigned char SE[13]={0x01,0xFD,0x01,0x00,0x3C,0x00,0x00,0x00,0x0C,0x80,0x00,0x01,0x6B};
unsigned char SE1[8]={0x01,0xF6,0x01,0x00,0x3C,0x00,0x01,0x6B};
unsigned char TB[4]={0x00,0xFF,0x66,0x6B};
unsigned char STP[5]={0x00,0xFE,0x98,0x00,0x6B};
unsigned char READ[3]={0x00,0x36,0x6B};
unsigned char READ_SPEED[3]={0x00,0x35,0x6B};  // 电机速度读取命令
unsigned char CLEAR[4]={0x01,0x0A,0x6D,0x6B};  // 电机清零命令
unsigned char cmd[16] = {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,};

#define		ABS(x)		((x) > 0 ? (x) : -(x))

// 根据电机ID选择对应的串口
static UART_Regs* get_motor_uart(uint8_t motor_id)
{
    switch(motor_id) {
        case 0x01: return UART_1_INST;  // 电机ID=0x01使用UART1
        case 0x02: return UART_3_INST;  // 电机ID=0x02使用UART3
        default: return UART_1_INST;    // 默认使用UART1
    }
}




void motor_init()
{
	RE[0]=0x01;
	uart_send_data(UART_1_INST,RE,6);
	delay_ms(10);
	RE[0]=0x02;
	uart_send_data(UART_3_INST,RE,6);
	delay_ms(10);
	motor_stop();
}
void motor_distance(uint8_t id,uint8_t turn,uint16_t speed,uint8_t accel,uint32_t pulse,uint8_t mode,uint8_t sync)
{
	SE[0]=id;
	SE[2]=turn;
	SE[3]=speed >> 8;
	SE[4]=speed;
	SE[5]=accel;
	SE[6]=pulse>>24;
	SE[7]=pulse>>16;
	SE[8]=pulse>>8;
	SE[9]=pulse;
	SE[10]=mode;
	SE[11]=sync;
	uart_send_data(get_motor_uart(id),SE,13);  // 根据电机ID选择串口
	delay_ms(10);
}


void motor_speed(uint8_t id,uint8_t turn,uint16_t speed,uint8_t accel,uint8_t sync)
{
	SE1[0]=id;
	SE1[2]=turn;
	SE1[3]=speed >> 8;
	SE1[4]=speed;
	SE1[5]=accel;
	SE1[6]=sync;
	uart_send_data(get_motor_uart(id),SE1,8);  // 根据电机ID选择串口
	delay_ms(10);
}




void motor_readdistance(uint8_t id)
{
	READ[0]=id;
	uart_send_data(get_motor_uart(id),READ,3);  // 根据电机ID选择串口
	delay_ms(10);
}

/**
 * @brief 电机速度读取函数
 *
 * 发送速度读取命令：地址 + 0x35 + 校验字节
 * - 地址: 电机ID (0x01或0x02)
 * - 0x35: 速度读取功能码
 * - 0x6B: 校验字节
 *
 * 返回格式：
 * - 正确返回：地址 + 0x35 + 符号 + 转速数据 + 校验字节 (6字节)
 * - 错误返回：地址 + 0x00 + 校验字节 (3字节)
 *
 * @param id 电机ID (0x01或0x02)
 */
void motor_readspeed(uint8_t id)
{
	READ_SPEED[0] = id;
	uart_send_data(get_motor_uart(id), READ_SPEED, 3);  // 根据电机ID选择串口
}
void motor_stop()
{
	// 停止所有电机，发送到两个串口
	uart_send_data(UART_1_INST,STP,5);  // 停止UART1上的电机
	delay_ms(10);
	uart_send_data(UART_3_INST,STP,5);  // 停止UART3上的电机
	delay_ms(10);
}

/**
 * @brief 电机位置清零函数
 *
 * 发送清零命令：ID 0A 6D 6B
 * - ID: 电机ID (0x01或0x02)
 * - 0x0A: 清零功能码
 * - 0x6D: 参数
 * - 0x6B: 校验字节
 *
 * @param id 电机ID (0x01或0x02)
 */
void motor_clear_position(uint8_t id)
{
	CLEAR[0] = id;  // 设置电机ID
	uart_send_data(get_motor_uart(id), CLEAR, 4);  // 根据电机ID选择串口
	delay_ms(10);
}

/**
 * @brief 所有电机位置清零函数
 *
 * 对所有电机(ID 0x01和0x02)执行位置清零操作
 */
void motor_clear_all_position(void)
{
	// 清零电机1 (ID=0x01)
	motor_clear_position(0x01);

	// 清零电机2 (ID=0x02)
	motor_clear_position(0x02);
}
void motor_sync()
{
	// 同步所有电机，发送到两个串口
	uart_send_data(UART_1_INST,TB,4);  // 同步UART1上的电机
	delay_ms(5);
	uart_send_data(UART_3_INST,TB,4);  // 同步UART3上的电机
	delay_ms(5);
}


#define MOTOR_MAX_SPEED 20 

// 高性能电机速度设置 - 优化版本
void Motor_Set_Speed(int8_t x_percent, int8_t y_percent)
{
    // 快速限幅 - 使用三元运算符减少分支
    x_percent = (x_percent > 100) ? 100 : ((x_percent < -100) ? -100 : x_percent);
    y_percent = (y_percent > 100) ? 100 : ((y_percent < -100) ? -100 : y_percent);

    // 方向和速度一次性计算 - 减少条件判断
    uint8_t x_dir = (x_percent < 0) ? 1 : 0;
    uint8_t y_dir = (y_percent < 0) ? 1 : 0;

    // 取绝对值 - 使用位运算优化
    uint8_t abs_x = (x_percent < 0) ? -x_percent : x_percent;
    uint8_t abs_y = (y_percent < 0) ? -y_percent : y_percent;

    // 预计算速度 - 使用正确的计算方式
    uint16_t x_speed = (abs_x * MOTOR_MAX_SPEED) / 100;  // 保持原有精度
    uint16_t y_speed = (abs_y * MOTOR_MAX_SPEED) / 100;  // 保持原有精度

//		 uart_printf(UART_2_INST, "E:X%d Y%d O:X%d Y%d T:X%d Y%d\r\n",
//                    error_x, error_y, motor_x, motor_y, target_x, target
    // 直接发送命令 - 减少函数调用开销
    motor_speed(0x01, x_dir, x_speed, 0, 0);
    motor_speed(0x02, y_dir, y_speed, 0, 0);
}


void dis_read(void)
{
	static uint8_t read_count = 0;
	if(++read_count == 3) read_count = 1;

	// 根据计数器读取不同电机的位置
	if(read_count == 1) {
		motor_readdistance(0x01);  // 读取电机1位置
	} else if(read_count == 2) {
		motor_readdistance(0x02);  // 读取电机2位置
	}
}


void motor_move_angle(float angle_x, float angle_y, uint8_t speed_percent, uint8_t accel)
{
    uint8_t x_dir, y_dir;
    uint32_t x_pulse, y_pulse;
    uint16_t actual_speed;

    // 限制速度百分比范围
    if(speed_percent > 100) speed_percent = 100;
    if(speed_percent < 1) speed_percent = 1;

    // 计算实际速度值(百分比转换为RPM，与Motor_Set_Speed保持一致)
    actual_speed = (uint16_t)((speed_percent * MOTOR_MAX_SPEED) / 100);

    // 确定X轴方向和脉冲数
    if(angle_x >= 0) {
        x_dir = 0;  // CW方向 (顺时针)
    } else {
        x_dir = 1;  // CCW方向 (逆时针)
        angle_x = -angle_x;  // 取绝对值
    }

    // 确定Y轴方向和脉冲数
    if(angle_y >= 0) {
        y_dir = 0;  // CW方向 (顺时针)
    } else {
        y_dir = 1;  // CCW方向 (逆时针)
        angle_y = -angle_y;  // 取绝对值
    }

    // 角度转脉冲数计算：使用控制分辨率
    // 控制分辨率：MOTOR_CONTROL_PULSES_PER_REV脉冲 = 360度 (16细分)
    // 读取分辨率：MOTOR_ENCODER_PULSES_PER_REV脉冲 = 360度 (编码器反馈)
    // 脉冲数 = 角度 * (MOTOR_CONTROL_PULSES_PER_REV / 360)
    x_pulse = (uint32_t)(angle_x * 3200 / 360.0f + 0.5f);  // +0.5f用于四舍五入
    y_pulse = (uint32_t)(angle_y * 3200 / 360.0f + 0.5f);  // +0.5f用于四舍五入

    // 发送X轴电机控制命令 (ID=1, 相对位置模式, 启用同步)
    motor_distance(0x01, x_dir, actual_speed, accel, x_pulse, 0, 1);

    // 发送Y轴电机控制命令 (ID=2, 相对位置模式, 启用同步)
    motor_distance(0x02, y_dir, actual_speed, accel, y_pulse, 0, 1);

    // 发送同步命令，两个电机同时开始运动
    motor_sync();
}

/**
 * @brief 使用示例函数
 *
 * 演示如何使用motor_move_angle函数控制电机移动
 *
 * 使用示例：
 * - motor_move_angle(90.5, -45.2, 50, 0);  // X轴转90.5度，Y轴转-45.2度，50%速度，无加速度
 * - motor_move_angle(180.0, 180.0, 80, 5); // 两轴各转180度，80%速度，加速度档位5
 */
void motor_angle_example(void)
{
    // 示例1：精确角度控制
    motor_move_angle(90.5f, -45.2f, 50, 0);  // X轴顺时针90.5度，Y轴逆时针45.2度，50%速度

    // 示例2：大角度移动
    motor_move_angle(180.0f, 180.0f, 80, 5); // 两轴各顺时针180度，80%速度，加速度档位5

    // 示例3：小角度微调
    motor_move_angle(1.5f, -0.8f, 20, 0);    // 小角度精确调整，20%低速
}

/**
 * @brief 测试串口自动选择功能
 *
 * 验证电机控制函数能够根据ID自动选择正确的串口
 */
void test_uart_auto_select(void)
{
    // 测试电机1 (ID=0x01, 应使用UART1)
    motor_speed(0x01, 0, 50, 0, 0);    // 电机1正转50RPM
    delay_ms(100);
    motor_readspeed(0x01);             // 读取电机1速度
    delay_ms(100);

    // 测试电机2 (ID=0x02, 应使用UART3)
    motor_speed(0x02, 1, 30, 0, 0);    // 电机2反转30RPM
    delay_ms(100);
    motor_readspeed(0x02);             // 读取电机2速度
    delay_ms(100);

    // 停止所有电机
    motor_stop();
}

