#include "Key_App.h"
#include "motor_app.h" // 包含normalize_angle_diff函数
#include "Task_func.h" // 包含open变量声明

uint8_t count_Tt=0;
extern uint8_t open;  // 声明为外部变量，在Task_func.c中定义

float angle_error=0.0f;
float reference_yaw = 0.0f; // 零度参考角
float count_return=0;
uint16_t xy_z[5][2]={0};

uint8_t Key_Read(void)
{
	uint8_t Key_num=0;
	if (DL_GPIO_readPins(GPIOB, DL_GPIO_PIN_21) == 0)Key_num=1;
//	if (DL_GPIO_readPins(GPIOA, DL_GPIO_PIN_24) == 0)Key_num=5;
	if (DL_GPIO_readPins(GPIOA, DL_GPIO_PIN_25) == 0)Key_num=4;
	if (DL_GPIO_readPins(GPIOA, DL_GPIO_PIN_26) == 0)Key_num=3;
	if (DL_GPIO_readPins(GPIOA, DL_GPIO_PIN_27) == 0)Key_num=2;
	return Key_num;
	
}

uint8_t Key_Down,Key_Up,Key_Val,Key_Old;

/**
 * @brief 更新angle_error为相对于参考角的角度差值
 */

uint32_t began_time=0;
uint8_t Key_Count=0;


void Key_Proc(void)
{
	Key_Val=Key_Read();
	Key_Down=Key_Val&(Key_Val^Key_Old);
	Key_Up=~Key_Val&(Key_Val^Key_Old);
	Key_Old=Key_Val;

	

	switch(Key_Down)
	{
		case 1:
				//DL_GPIO_setPins(GPIOB, DL_GPIO_PIN_26);
        //delay_ms(1000);//延时大概1S
    
			Read_L();
			pid_target(318,225);
			open=1;
			break;
		case 2:

			break;
		case 3:
			Laser_open();
			break;
		case 4:
			Laser_set();
			break;

	}

	// 正弦波绘制功能已移至按键3控制

}


