# 二维云台激光正弦波绘制使用说明

## 功能概述
本系统可以控制二维云台配合激光笔绘制正弦函数波形图。

## 操作方法

### 1. 基本操作
- **按键1**: 原有功能（state_up）
- **按键2**: 原有功能已注释（Read_L等）
- **按键3**: 正弦波绘制开始/停止控制
- **按键4**: 原有功能（设置坐标点）

### 2. 正弦波模式
按键3控制正弦波绘制，使用标准正弦波模式：

**标准正弦波参数**
- 起始中心点: ((xy[0][0]+xy[3][0])/2, (xy[0][1]+xy[3][1])/2)
- 振幅: 根据xy[0][1]和xy[2][1]自动计算
- 频率: 1个完整周期
- 相位: 0°
- 绘制点数: 100
- X绘制范围: xy[0][0]到xy[2][0]
- Y绘制范围: xy[0][1]到xy[2][1]

### 3. 操作流程
1. 确保云台已正确连接并初始化
2. 将激光笔安装在云台上
3. 移动云台到合适的起始位置
4. 按下按键3开始正弦波绘制
5. 系统会自动开启激光并开始绘制
6. 绘制完成后激光会自动关闭
7. 如需中途停止，再次按下按键3

### 4. 注意事项
- 激光笔为常开状态，无需额外控制
- 绘制过程中请勿移动设备
- 确保xy坐标范围已正确设置
- 激光使用时注意安全，避免直射眼睛

### 5. 自定义参数
如需修改正弦波参数，可以调用以下函数：
```c
sine_wave_draw(amplitude, frequency, phase, points);
```

参数说明：
- amplitude: 振幅（Y轴最大偏移）
- frequency: 频率（完整周期数）
- phase: 相位（弧度制）
- points: 绘制点数

### 6. 串口调试信息
系统会通过UART0输出调试信息，包括：
- 当前选择的模式
- 绘制进度
- 目标坐标
- 完成状态

波特率请参考系统配置。
