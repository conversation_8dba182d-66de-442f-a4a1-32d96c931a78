#ifndef _MYDEFINE_H_
#define _MYDEFINE_H_

#include "ti_msp_dl_config.h"

#include <stdarg.h>
#include <stdio.h>
#include "ti_msp_dl_config.h"  // ����UART��ض���ͺ���
#include <stdint.h>

#include "string.h"

#include "usart_mid.h"
#include "scheduler.h"
#include "motor_mid.h"


#include <stdlib.h>  
#include "Key_App.h"
#include "motor_app.h"
#include "Task_func.h"
#include "JY61P.h"


//#include "calibration.h"
//#include "calibration_app.h"



#endif



