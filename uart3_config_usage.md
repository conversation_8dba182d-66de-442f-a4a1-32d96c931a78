# UART3 串口配置使用说明

## 概述
UART3已配置为与UART1相同的超时解析模式，支持电机数据包的接收和解析。

## 配置参数

### 基本配置
- **数据包大小**: 64字节 (`UART3_PACKET_SIZE`)
- **超时时间**: 3ms (`UART3_TIMEOUT_MS`)
- **缓冲区类型**: 滑动缓冲区，支持不完整数据包处理

### 中断配置
- 使用接收中断 (`DL_UART_INTERRUPT_RX`)
- 中断处理函数: `UART_3_INST_IRQHandler()`
- 支持索引溢出保护

## 功能特性

### 1. 超时解析机制
- 自动检测数据接收完成
- 支持不定长数据包
- 防止数据丢失和缓冲区溢出

### 2. 数据处理
- 使用与UART1相同的电机数据包解析函数
- 支持电机位置、速度等数据解析
- 兼容现有电机通信协议

### 3. 调度任务
- 超时检查任务: 2ms周期
- 数据处理任务: 3ms周期
- 与UART1保持相同的处理频率

## 使用方法

### 初始化
```c
// 在UART_DMA_Init()中自动调用
UART3_Timeout_Init();
```

### 数据处理
系统会自动在调度器中处理UART3数据：
- `UART3_CheckTimeout()` - 检查接收超时
- `UART3_ProcessData()` - 处理接收到的数据

### 测试功能
```c
// 测试UART3电机数据解析
Test_UART3_MotorData();
```

## 配置文件修改

### usart_mid.h
- 添加UART3相关宏定义
- 添加UART3_Handle_t结构体
- 添加UART3函数声明

### usart_mid.c
- 添加gUart3Handle全局变量
- 实现UART3超时解析函数
- 添加UART3中断处理函数
- 添加UART3测试函数

### scheduler.c
- 添加UART3超时检查任务
- 添加UART3数据处理任务

## 注意事项

1. **硬件配置**: 确保UART_3_INST在硬件配置文件中正确定义
2. **中断优先级**: 建议与UART1保持相同的中断优先级
3. **数据格式**: 使用与UART1相同的电机通信协议
4. **缓冲区管理**: 系统自动管理缓冲区，无需手动清理

## 电机控制串口分配

### 自动串口选择
所有电机控制函数现在根据电机ID自动选择对应的串口：
- **电机ID = 0x01**: 使用UART1发送控制指令
- **电机ID = 0x02**: 使用UART3发送控制指令

### 修改的函数
以下函数已更新为支持自动串口选择：
- `motor_distance()` - 电机位置控制
- `motor_speed()` - 电机速度控制
- `motor_readdistance()` - 读取电机位置
- `motor_readspeed()` - 读取电机速度
- `motor_clear_position()` - 电机位置清零

### 全局控制函数
- `motor_stop()` - 停止所有电机（发送到UART1和UART3）
- `motor_sync()` - 同步所有电机（发送到UART1和UART3）
- `motor_clear_all_position()` - 清零所有电机位置

### 使用示例
```c
// 控制电机1 (通过UART1)
motor_speed(0x01, 0, 100, 0, 0);

// 控制电机2 (通过UART3)
motor_speed(0x02, 1, 50, 0, 0);

// 读取电机1位置 (通过UART1)
motor_readdistance(0x01);

// 读取电机2位置 (通过UART3)
motor_readdistance(0x02);
```

## 兼容性
- 完全兼容现有UART1的电机控制功能
- 支持相同的数据包格式和解析逻辑
- 可与UART1同时使用，互不干扰
- 自动根据电机ID选择正确的串口，无需手动指定
