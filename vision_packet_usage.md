# 视觉数据包解析功能使用说明

## 功能概述

本模块提供了完整的视觉数据包解析功能，支持两种类型的数据包：
1. **红色目标检测数据包** (CMD = 0x02)
2. **矩形检测数据包** (CMD = 0x01)

## 数据包格式

### 通用格式
```
+--------+--------+----------+----------+----------+----------+
| 帧头1  | 帧头2  | 命令类型 | 数据长度 | 数据内容 | 校验和   |
+--------+--------+----------+----------+----------+----------+
|  0xAA  |  0x55  |   CMD    |   LEN    |   DATA   |   CHK    |
+--------+--------+----------+----------+----------+----------+
```

### 红色目标检测数据包 (CMD = 0x02)
```
0xAA 0x55 0x02 0x02 X Y CHK
```
- X, Y: 目标中心点坐标 (0-255)
- CHK: 校验和 = (2 + X + Y + 4) & 0xFF

### 矩形检测数据包 (CMD = 0x01)
```
0xAA 0x55 0x01 0x08 X1 Y1 X2 Y2 X3 Y3 X4 Y4 CHK
```
- X1,Y1 ~ X4,Y4: 四个关键点坐标 (0-255)
- CHK: 校验和 = (1 + X1 + Y1 + X2 + Y2 + X3 + Y3 + X4 + Y4 + 8) & 0xFF

## API 接口

### 核心解析函数
```c
VisionParseResult_t parse_vision_packet(const uint8_t* buffer, uint16_t buffer_len, VisionPacketData_t* result);
```
- **功能**: 解析视觉数据包
- **参数**: 
  - `buffer`: 接收到的数据缓冲区
  - `buffer_len`: 缓冲区长度
  - `result`: 解析结果输出
- **返回值**: 解析状态 (VISION_PARSE_OK 表示成功)

### 数据获取函数
```c
// 获取红色目标坐标
bool get_red_target_coordinates(const VisionPacketData_t* packet_data, uint8_t* x, uint8_t* y);

// 获取矩形关键点坐标
bool get_rectangle_coordinates(const VisionPacketData_t* packet_data, 
                              uint8_t* x1, uint8_t* y1, uint8_t* x2, uint8_t* y2,
                              uint8_t* x3, uint8_t* y3, uint8_t* x4, uint8_t* y4);
```

### 错误处理函数
```c
const char* get_vision_parse_error_string(VisionParseResult_t result);
```

## 使用示例

### 基本使用方法
```c
void your_uart_receive_handler(uint8_t* received_data, uint16_t data_len)
{
    VisionPacketData_t vision_data;
    VisionParseResult_t result;
    
    // 解析数据包
    result = parse_vision_packet(received_data, data_len, &vision_data);
    
    if(result == VISION_PARSE_OK) {
        switch(vision_data.packet_type) {
            case VISION_CMD_RED_TARGET: {
                uint8_t x, y;
                if(get_red_target_coordinates(&vision_data, &x, &y)) {
                    // 处理红色目标坐标
                    printf("Red target at: (%d, %d)\n", x, y);
                    // 添加您的控制逻辑
                }
                break;
            }
            
            case VISION_CMD_RECTANGLE: {
                uint8_t x1, y1, x2, y2, x3, y3, x4, y4;
                if(get_rectangle_coordinates(&vision_data, &x1, &y1, &x2, &y2, &x3, &y3, &x4, &y4)) {
                    // 处理矩形坐标
                    printf("Rectangle points: (%d,%d) (%d,%d) (%d,%d) (%d,%d)\n", 
                           x1, y1, x2, y2, x3, y3, x4, y4);
                    // 添加您的控制逻辑
                }
                break;
            }
        }
    } else {
        printf("Parse error: %s\n", get_vision_parse_error_string(result));
    }
}
```

### 在现有串口中断中集成
```c
// 在 UART_X_INST_IRQHandler 中调用
void UART_X_INST_IRQHandler(void)
{
    // ... 现有的接收逻辑 ...
    
    // 当接收完成时，调用解析函数
    if(data_received_complete) {
        vision_packet_parse_example(uart_buffer, buffer_length);
    }
}
```

## 测试功能

### 运行测试
```c
// 调用测试函数验证解析功能
test_vision_packet_parser();
```

测试函数会验证两种数据包格式的解析，并通过串口输出结果。

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| VISION_PARSE_OK | 解析成功 |
| VISION_PARSE_INVALID_HEADER | 无效帧头 (不是 0xAA 0x55) |
| VISION_PARSE_INVALID_LENGTH | 数据长度错误 |
| VISION_PARSE_CHECKSUM_ERROR | 校验和错误 |
| VISION_PARSE_UNKNOWN_CMD | 未知命令类型 |

## 注意事项

1. **内存安全**: 所有函数都进行了参数检查，避免空指针访问
2. **校验和验证**: 自动计算并验证校验和，确保数据完整性
3. **局部变量**: 解析过程中尽量使用局部变量，减少全局变量使用
4. **错误处理**: 提供详细的错误状态和描述信息
5. **扩展性**: 结构设计支持后续添加新的数据包类型

## 集成建议

1. 在串口接收中断或DMA完成中断中调用解析函数
2. 根据解析结果执行相应的控制逻辑
3. 使用错误处理机制确保系统稳定性
4. 可以根据需要修改示例函数中的处理逻辑
