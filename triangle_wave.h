#ifndef _TRIANGLE_WAVE_H_
#define _TRIANGLE_WAVE_H_

#include "mydefine.h"

// 三角形运动状态枚举
typedef enum {
    TRIANGLE_IDLE = 0,          // 空闲状态
    TRIANGLE_MOVING_TO_POINT,   // 移动到指定点
    TRIANGLE_COMPLETED,         // 运动完成
    TRIANGLE_ERROR              // 错误状态
} TriangleState_t;

// 三角形运动模式枚举
typedef enum {
    TRIANGLE_MODE_ONCE = 0,     // 单次运行
    TRIANGLE_MODE_LOOP,         // 循环运行
    TRIANGLE_MODE_PINGPONG      // 往返运行
} TriangleMode_t;

// 三角形运动参数结构体
typedef struct {
    int16_t point_a[2];         // 顶点A坐标 [x, y]
    int16_t point_b[2];         // 顶点B坐标 [x, y]
    int16_t point_c[2];         // 顶点C坐标 [x, y]
    uint16_t move_speed;        // 移动速度 (1-100%)
    uint16_t dwell_time_ms;     // 在每个顶点的停留时间(毫秒)
    uint8_t deadzone;           // 到达判定死区(像素)
    TriangleMode_t mode;        // 运动模式
    uint8_t laser_enable;       // 激光使能标志
} TriangleParams_t;

// 三角形运动控制结构体
typedef struct {
    TriangleParams_t params;    // 运动参数
    TriangleState_t state;      // 当前状态
    uint8_t current_point;      // 当前目标点索引 (0=A, 1=B, 2=C)
    uint8_t cycle_count;        // 循环计数
    uint32_t dwell_start_time;  // 停留开始时间
    uint8_t is_active;          // 运动激活标志
    uint8_t direction;          // 运动方向 (0=正向A->B->C, 1=反向C->B->A)
} TriangleControl_t;

// 外部变量声明
extern TriangleControl_t triangle_ctrl;
extern uint8_t triangle_wave_mode;

// 函数声明

/**
 * @brief 三角形运动模块初始化
 */
void triangle_wave_init(void);

/**
 * @brief 设置三角形的三个顶点坐标
 * @param ax 顶点A的X坐标
 * @param ay 顶点A的Y坐标
 * @param bx 顶点B的X坐标
 * @param by 顶点B的Y坐标
 * @param cx 顶点C的X坐标
 * @param cy 顶点C的Y坐标
 */
void triangle_set_points(int16_t ax, int16_t ay, int16_t bx, int16_t by, int16_t cx, int16_t cy);

/**
 * @brief 设置三角形运动参数
 * @param speed 移动速度 (1-100%)
 * @param dwell_time 在每个顶点的停留时间(毫秒)
 * @param deadzone 到达判定死区(像素)
 * @param mode 运动模式
 * @param laser_enable 激光使能标志
 */
void triangle_set_params(uint16_t speed, uint16_t dwell_time, uint8_t deadzone, 
                        TriangleMode_t mode, uint8_t laser_enable);

/**
 * @brief 开始三角形运动
 * @return 0=成功, 1=参数错误, 2=系统忙
 */
uint8_t triangle_start(void);

/**
 * @brief 停止三角形运动
 */
void triangle_stop(void);

/**
 * @brief 暂停三角形运动
 */
void triangle_pause(void);

/**
 * @brief 恢复三角形运动
 */
void triangle_resume(void);

/**
 * @brief 三角形运动任务 (需要在主循环中调用)
 * 建议调用频率: 1ms
 */
void triangle_wave_task(void);

/**
 * @brief 获取三角形运动状态
 * @return 当前运动状态
 */
TriangleState_t triangle_get_state(void);

/**
 * @brief 获取当前目标点信息
 * @param point_index 输出当前目标点索引 (0=A, 1=B, 2=C)
 * @param cycle_count 输出循环计数
 * @return 0=成功, 1=运动未激活
 */
uint8_t triangle_get_current_info(uint8_t *point_index, uint8_t *cycle_count);

/**
 * @brief 检查是否到达指定点
 * @param target_x 目标X坐标
 * @param target_y 目标Y坐标
 * @return 1=已到达, 0=未到达
 */
uint8_t triangle_is_point_reached(int16_t target_x, int16_t target_y);

// 预设三角形函数

/**
 * @brief 预设1: 标准等边三角形 (中心在当前位置，边长100)
 */
void triangle_preset_equilateral(void);

/**
 * @brief 预设2: 直角三角形 (底边水平，高度100)
 */
void triangle_preset_right_angle(void);

/**
 * @brief 预设3: 自定义大小的等边三角形
 * @param center_x 中心X坐标
 * @param center_y 中心Y坐标
 * @param side_length 边长
 */
void triangle_preset_custom_equilateral(int16_t center_x, int16_t center_y, uint16_t side_length);

#endif /* _TRIANGLE_WAVE_H_ */
