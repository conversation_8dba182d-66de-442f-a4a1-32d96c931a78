#ifndef _SINE_WAVE_H_
#define _SINE_WAVE_H_

#include "mydefine.h"

//=============================================================================
// 常量定义
//=============================================================================
#define MOTION_MODE_TRIANGLE    0   // 三角形运动
#define MOTION_MODE_SINE        1   // 正弦波运动
#define MOTION_DEADZONE         4   // 通用死区大小

//=============================================================================
// 主要接口函数 (外部调用)
//=============================================================================

// 初始化函数
void sine_wave_init(void);

// 统一运动控制接口
void unified_motion_task(void);     // 统一运动任务 (在调度器中调用)
void set_motion_mode(uint8_t mode); // 设置运动模式
void start_motion(void);            // 开始运动
void stop_motion(void);             // 停止运动

//=============================================================================
// 正弦波运动函数
//=============================================================================
void sine_wave_task(void);
void sine_wave_stop(void);
void sine_wave_preset_1(void);      // 标准正弦波预设
void sine_wave_pid_control(void);

// 正弦波参数设置函数
void sine_wave_set_params(float amplitude, float frequency, float phase, uint16_t points);
void sine_wave_set_center(int16_t center_x, int16_t center_y);
void sine_wave_calculate_point(uint16_t index, int16_t *x, int16_t *y);

//=============================================================================
// 三角形运动函数
//=============================================================================
void triangle_set_points(void);
void triangle_start(void);
void triangle_stop(void);
void triangle_task(void);
void triangle_pid_control(void);

//=============================================================================
// 通用函数
//=============================================================================
void common_pid_control(const char* mode_name);
void common_motion_stop(void);
uint8_t is_target_reached(uint8_t deadzone);
void laser_control(uint8_t state);

//=============================================================================
// 外部变量声明
//=============================================================================
extern uint8_t motion_mode;        // 当前运动模式
extern uint8_t sine_wave_mode;     // 正弦波模式标志
extern uint8_t triangle_mode;      // 三角形模式标志
extern uint8_t laser_state;        // 激光状态

#endif /* _SINE_WAVE_H_ */
