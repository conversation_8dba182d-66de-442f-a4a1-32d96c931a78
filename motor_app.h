#ifndef _MCTRL_H_
#define _MCTRL_H_

#include "mydefine.h"

void read_place(int x, int y);

void app_pid_calc(void);
void app_pid_calc_fast(void);  // 高性能PID计算函数

void app_pid_init(void);
void pid_target(int x, int y);
void Read_L(void);
void get_data(uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2,uint16_t x3, uint16_t y3, uint16_t x4, uint16_t y4);

void Laser_open();
void Laser_close();
void Laser_set();


void laser_control(uint8_t state);
extern int target_x ; // 默认屏幕中心
extern int target_y ; // 默认屏幕中心
extern uint16_t xy[5][2];
extern int current_x;
extern int current_y;
extern uint8_t sine_wave_mode;
extern uint8_t laser_state;
void triangle_set_points_from_current(int side_length);
void import_points(int points[][2], uint8_t count);

void start_circle(int cx, int cy, int radius, int steps);
void start_square(int cx, int cy, int side_length);

#endif

