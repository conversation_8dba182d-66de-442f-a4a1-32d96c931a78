#ifndef _MID_MOTOR_H_
#define _MID_MOTOR_H_
#include "mydefine.h"
void motor_init(void);
void motor_distance(uint8_t id,uint8_t turn,uint16_t speed,uint8_t accel,uint32_t pulse,uint8_t mode,uint8_t sync);
void motor_speed(uint8_t id,uint8_t turn,uint16_t speed,uint8_t accel,uint8_t sync);
void motor_readdistance(uint8_t id);
void motor_readspeed(uint8_t id);
void motor_sync(void);
void motor_stop(void);
void motor_clear_position(uint8_t id);
void motor_clear_all_position(void);
void Motor_Set_Speed(int8_t x_percent, int8_t y_percent);
void dis_read(void);
void motor_curve(uint8_t addr, uint8_t dir, uint16_t acc, uint16_t dec, float velocity, float position, uint8_t raf, uint8_t snF);
void motor_move_angle(float angle_x, float angle_y, uint8_t speed_percent, uint8_t accel);
void motor_angle_example(void);
void test_uart_auto_select(void);  // 测试串口自动选择功能

// 简化的角度像素转换函数
int angle_to_pixel_x(float angle);
int angle_to_pixel_y(float angle);
float pixel_to_angle_x(int pixel_x);
float pixel_to_angle_y(int pixel_y);
void move_to_pixel(int pixel_x, int pixel_y, uint8_t speed);

#endif
