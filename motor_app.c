#include "motor_app.h"
#include "pid_mid.h"
#include "usart_mid.h"
#include "math.h"







// PID目标坐标
int target_x = 0; // 默认屏幕中心
int target_y = 0; // 默认屏幕中心

// 当前实际坐标
int current_x = 0;
int current_y = 0;

// 电机输出值
int8_t motor_x, motor_y;


PID_T pid_x; // X轴PID控制器
PID_T pid_y; // Y轴PID控制器

PidParams_t pid_params_x = {
    .kp =1.4f,   // 增大Kp提高响应速度
    .ki = 0.000f, // 增加积分项减小稳态误差
    .kd = 0.02f, // 增加微分项提高稳定性
    .sample_time = 0.01f,
    .out_min = -99.0f,
    .out_max = 99.0f,
    .i_min = -80.0f,
    .i_max = 80.0f,
    .deadzone = 3 // 减小死区大小，提高精度
};

PidParams_t pid_params_y = {
    .kp = 1.4f,   // 增大Kp提高响应速度
    .ki = 0.000f, // 增加积分项减小稳态误差
    .kd = 0.015f, // 增加微分项提高稳定性
    .sample_time = 0.01f,
    .out_min = -99.0f,
    .out_max = 99.0f,
    .i_min = -80.0f,
    .i_max = 80.0f,
    .deadzone = 3 // 减小死区大小，提高精度
};

void app_pid_init(void)
{
    // 初始化X轴PID控制器
    pid_init(&pid_x,
             pid_params_x.kp, pid_params_x.ki, pid_params_x.kd,
             (float)current_x, pid_params_x.out_max);
	
    // 初始化Y轴PID控制器
    pid_init(&pid_y,
             pid_params_y.kp, pid_params_y.ki, pid_params_y.kd,
             (float)current_y, pid_params_y.out_max);
}


void read_place(int x, int y)
{
	
	current_x=x;
	current_y=y;
}

// 快速饱和处理宏
#define CONSTRAIN(x, min, max) ((x) < (min) ? (min) : ((x) > (max) ? (max) : (x)))

void pid_target(int x, int y)
{
	target_x = x;
	target_y = y;
	
	    // 使用我们的PID中间件设置目标值
  pid_set_target(&pid_x, (float)target_x);
  pid_set_target(&pid_y, (float)target_y);
}

#define MAX_POINTS 50


void Laser_open()
{
	 DL_GPIO_setPins(GPIOB, DL_GPIO_PIN_26);
}
void Laser_close()
{
	DL_GPIO_clearPins(GPIOB, DL_GPIO_PIN_26);
}
void Laser_set()
{
	 DL_GPIO_setPins(GPIOB, DL_GPIO_PIN_26);
	 delay_ms(20);
	 DL_GPIO_clearPins(GPIOB, DL_GPIO_PIN_26);
}

// 高性能PID计算 - 内联优化版本
static inline float fast_pid_calc(float kp, float ki, float kd, float error, float *integral, float *last_error, float i_min, float i_max)
{
    *integral += error;  // 积分累加
    if(*integral > i_max) *integral = i_max;  // 积分限幅
    else if(*integral < i_min) *integral = i_min;

    float derivative = error - *last_error;  // 微分计算
    *last_error = error;  // 更新上次误差

    return kp * error + ki * (*integral) + kd * derivative;  // PID输出
}

void app_pid_calc(void)
{
    // 使用局部变量减少内存访问
    int error_x = target_x - current_x;
    int error_y = target_y - current_y;

    // 快速死区判断 - 使用位运算优化
    int abs_error_x = error_x < 0 ? -error_x : error_x;
    int abs_error_y = error_y < 0 ? -error_y : error_y;

    if (abs_error_x <= 3 && abs_error_y <= 3)  // 硬编码死区值，避免结构体访问
    {
        pid_x.integral = 0;  // 清空积分
        pid_y.integral = 0;
        motor_stop();
        return;
    }

    // 高性能PID计算 - 避免函数调用开销
    float output_x = fast_pid_calc(1.4f, 0.0f, 0.02f, (float)error_x,
                                   &pid_x.integral, &pid_x.last_error, -80.0f, 80.0f);
    float output_y = fast_pid_calc(1.4f, 0.0f, 0.015f, (float)error_y,
                                   &pid_y.integral, &pid_y.last_error, -80.0f, 80.0f);

    // 快速限幅 - 避免函数调用
    if(output_x > 99.0f) output_x = 99.0f;
    else if(output_x < -99.0f) output_x = -99.0f;

    if(output_y > 99.0f) output_y = 99.0f;
    else if(output_y < -99.0f) output_y = -99.0f;

    // 直接转换并输出
    motor_x = (int8_t)output_x;
    motor_y = (int8_t)output_y;

    // 减少串口输出频率 - 每10次输出一次调试信息
    static uint8_t debug_counter = 0;
    if(++debug_counter >= 10) {
        debug_counter = 0;
        uart_printf(UART_2_INST, "E:X%d Y%d O:X%d Y%d T:X%d Y%d\r\n",
                    error_x, error_y, motor_x, motor_y, target_x, target_y);
    }

    Motor_Set_Speed(motor_x, -motor_y);
}

// 超高性能PID计算 - 极致优化版本
void app_pid_calc_fast(void)
{
    // 静态变量避免重复初始化
    static float x_integral = 0, y_integral = 0;
    static float x_last_error = 0, y_last_error = 0;
    static uint8_t skip_counter = 0;

    // 计算误差
    int error_x = target_x - current_x;
    int error_y = target_y - current_y;

    // 快速死区判断 - 使用绝对值
    int abs_error_x = (error_x < 0) ? -error_x : error_x;
    int abs_error_y = (error_y < 0) ? -error_y : error_y;

    if (abs_error_x <= 3 && abs_error_y <= 3)
    {
        x_integral = y_integral = 0;  // 清空积分
        motor_stop();
        return;
    }

    // 积分计算和限幅
    x_integral += error_x;
    if(x_integral > 80.0f) x_integral = 80.0f;
    else if(x_integral < -80.0f) x_integral = -80.0f;

    y_integral += error_y;
    if(y_integral > 80.0f) y_integral = 80.0f;
    else if(y_integral < -80.0f) y_integral = -80.0f;

    // PID计算 - 硬编码参数避免结构体访问
    float output_x = 1.4f * error_x + 0.02f * (error_x - x_last_error);
    float output_y = 1.4f * error_y + 0.015f * (error_y - y_last_error);

    x_last_error = error_x;
    y_last_error = error_y;

    // 快速限幅
    output_x = (output_x > 99.0f) ? 99.0f : ((output_x < -99.0f) ? -99.0f : output_x);
    output_y = (output_y > 99.0f) ? 99.0f : ((output_y < -99.0f) ? -99.0f : output_y);

    // 直接转换
    motor_x = (int8_t)output_x;
    motor_y = (int8_t)output_y;

    // 减少调试输出频率
    if(++skip_counter >= 20) {
        skip_counter = 0;
        uart_printf(UART_2_INST, "F:X%d Y%d\r\n", motor_x, motor_y);
    }

    Motor_Set_Speed(motor_x, -motor_y);
}




void Read_L(void)
{
	
 uart_printf(UART_2_INST, "Now: X=%d, Y=%d\r\n", current_x, current_y);
	
}










































































