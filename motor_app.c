#include "motor_app.h"
#include "pid_mid.h"
#include "usart_mid.h"
#include "math.h"







// PID目标坐标
int target_x = 0; // 默认屏幕中心
int target_y = 0; // 默认屏幕中心

// 当前实际坐标
int current_x = 0;
int current_y = 0;

// 电机输出值
int8_t motor_x, motor_y;


PID_T pid_x; // X轴PID控制器
PID_T pid_y; // Y轴PID控制器

PidParams_t pid_params_x = {
    .kp =1.4f,   // 增大Kp提高响应速度
    .ki = 0.000f, // 增加积分项减小稳态误差
    .kd = 0.02f, // 增加微分项提高稳定性
    .sample_time = 0.01f,
    .out_min = -99.0f,
    .out_max = 99.0f,
    .i_min = -80.0f,
    .i_max = 80.0f,
    .deadzone = 3 // 减小死区大小，提高精度
};

PidParams_t pid_params_y = {
    .kp = 1.4f,   // 增大Kp提高响应速度
    .ki = 0.000f, // 增加积分项减小稳态误差
    .kd = 0.015f, // 增加微分项提高稳定性
    .sample_time = 0.01f,
    .out_min = -99.0f,
    .out_max = 99.0f,
    .i_min = -80.0f,
    .i_max = 80.0f,
    .deadzone = 3 // 减小死区大小，提高精度
};

void app_pid_init(void)
{
    // 初始化X轴PID控制器
    pid_init(&pid_x,
             pid_params_x.kp, pid_params_x.ki, pid_params_x.kd,
             (float)current_x, pid_params_x.out_max);
	
    // 初始化Y轴PID控制器
    pid_init(&pid_y,
             pid_params_y.kp, pid_params_y.ki, pid_params_y.kd,
             (float)current_y, pid_params_y.out_max);
}


void read_place(int x, int y)
{
	
	current_x=x;
	current_y=y;
}

// 快速饱和处理宏
#define CONSTRAIN(x, min, max) ((x) < (min) ? (min) : ((x) > (max) ? (max) : (x)))

void pid_target(int x, int y)
{
	target_x = x;
	target_y = y;
	
	    // 使用我们的PID中间件设置目标值
  pid_set_target(&pid_x, (float)target_x);
  pid_set_target(&pid_y, (float)target_y);
}

#define MAX_POINTS 50


void Laser_open()
{
	 DL_GPIO_setPins(GPIOB, DL_GPIO_PIN_26);
}
void Laser_close()
{
	DL_GPIO_clearPins(GPIOB, DL_GPIO_PIN_26);
}
void Laser_set()
{
	 DL_GPIO_setPins(GPIOB, DL_GPIO_PIN_26);
	 delay_ms(20);
	 DL_GPIO_clearPins(GPIOB, DL_GPIO_PIN_26);
}

void app_pid_calc(void)
{
    int error_x = target_x - current_x;
    int error_y = target_y - current_y;
    float output_x, output_y;

    // === 死区判断：到当前点则直接切换下一个点 ===
    if (abs(error_x) <= pid_params_x.deadzone && abs(error_y) <= pid_params_y.deadzone)
		{
    // 清空积分避免残留
    pid_x.integral = 0;
    pid_y.integral = 0;

    //open = 0;
    motor_stop();
		//Laser_set();
		return ;
		}
    // === PID计算 ===
    output_x = pid_calculate_positional(&pid_x, (float)current_x);
    app_pid_limit_integral(&pid_x, pid_params_x.i_min, pid_params_x.i_max);

    output_y = pid_calculate_positional(&pid_y, (float)current_y);
    app_pid_limit_integral(&pid_y, pid_params_y.i_min, pid_params_y.i_max);

    // 限幅
    output_x = CONSTRAIN(output_x, pid_params_x.out_min, pid_params_x.out_max);
    output_y = CONSTRAIN(output_y, pid_params_y.out_min, pid_params_y.out_max);

    // 电机输出（连续驱动）
    motor_x = (int8_t)output_x;
    motor_y = (int8_t)output_y;

    uart_printf(UART_2_INST, "误差X=%d Y=%d 输出X=%d Y=%d 目标X=%d Y=%d\r\n",
                error_x, error_y, motor_x, motor_y, target_x, target_y);

    Motor_Set_Speed(motor_x,-motor_y);
}



void Read_L(void)
{
	
 uart_printf(UART_2_INST, "Now: X=%d, Y=%d\r\n", current_x, current_y);
	
}










































































